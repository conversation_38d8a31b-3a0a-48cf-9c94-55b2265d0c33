<template>
  <div class="workflow-task-list">
    <!-- 筛选器 -->
    <div class="filter-section q-pa-md">
      <div class="row q-gutter-md">
        <q-select
          v-model="filters.trainingType"
          :options="trainingTypeOptions"
          label="训练类型"
          emit-value
          map-options
          clearable
          dense
          style="min-width: 120px"
          @update:model-value="fetchTasks"
        />
        <q-select
          v-model="filters.status"
          :options="statusOptions"
          label="任务状态"
          emit-value
          map-options
          clearable
          dense
          style="min-width: 120px"
          @update:model-value="fetchTasks"
        />
        <q-btn
          icon="refresh"
          color="primary"
          dense
          round
          @click="fetchTasks"
          :loading="loading"
        />
      </div>
    </div>

    <!-- 任务列表 -->
    <div class="task-list-container">
      <q-list separator>
        <q-item
          v-for="task in taskList"
          :key="task.task_id"
          clickable
          v-ripple
          @click="handleTaskClick(task)"
          :class="{ 'selected-task': selectedTaskId === task.task_id }"
        >
          <q-item-section>
            <q-item-label class="task-name">{{ task.name }}</q-item-label>
            <q-item-label caption class="task-info">
              <div class="row items-center q-gutter-xs">
                <q-chip
                  :color="getTrainingTypeColor(task.training_type)"
                  text-color="white"
                  dense
                  size="sm"
                >
                  {{ getTrainingTypeDisplay(task.training_type) }}
                </q-chip>
                <q-chip
                  :color="getStatusColor(task.status)"
                  text-color="white"
                  dense
                  size="sm"
                >
                  {{ task.status_display }}
                </q-chip>
              </div>
              <div class="q-mt-xs">
                步骤{{ task.current_step }}: {{ task.step_name }}
              </div>
              <div class="q-mt-xs text-grey-6">
                创建时间: {{ formatDate(task.created_at) }}
              </div>
            </q-item-label>
          </q-item-section>

          <q-item-section side>
            <div class="column items-end q-gutter-xs">
              <!-- 进度条 -->
              <q-linear-progress
                :value="task.progress_percentage / 100"
                color="primary"
                size="8px"
                style="width: 80px"
              />
              <div class="text-caption">{{ task.progress_percentage }}%</div>
              
              <!-- 训练状态 (如果有) -->
              <q-chip
                v-if="task.training_status && task.training_status !== 'not_started'"
                :color="getTrainingStatusColor(task.training_status)"
                text-color="white"
                dense
                size="xs"
              >
                {{ task.training_status_display }}
              </q-chip>
            </div>
          </q-item-section>
        </q-item>
      </q-list>

      <!-- 空状态 -->
      <div v-if="!loading && taskList.length === 0" class="empty-state q-pa-lg text-center">
        <q-icon name="inbox" size="48px" color="grey-5" />
        <div class="q-mt-md text-grey-6">暂无训练任务</div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state q-pa-lg text-center">
        <q-spinner color="primary" size="32px" />
        <div class="q-mt-md">加载中...</div>
      </div>
    </div>

    <!-- 分页 -->
    <div v-if="totalTasks > pageSize" class="pagination-section q-pa-md">
      <q-pagination
        v-model="currentPage"
        :max="Math.ceil(totalTasks / pageSize)"
        direction-links
        boundary-links
        @update:model-value="fetchTasks"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useWorkflowStore } from 'src/stores/workflowStore'
import { 
  TRAINING_TYPES, 
  WORKFLOW_STATUS, 
  TRAINING_STATUS,
  STATUS_DISPLAY,
  TRAINING_STATUS_DISPLAY,
  getPageRoute 
} from 'src/services/workflowApi'
import { date } from 'quasar'

const props = defineProps({
  selectedTaskId: {
    type: String,
    default: null
  }
})

const emit = defineEmits(['task-selected'])

const router = useRouter()
const workflowStore = useWorkflowStore()

// 响应式数据
const taskList = ref([])
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const totalTasks = ref(0)

// 筛选器
const filters = ref({
  trainingType: null,
  status: null
})

// 训练类型选项
const trainingTypeOptions = [
  { label: '深度学习', value: TRAINING_TYPES.DEEP_LEARNING },
  { label: '强化学习', value: TRAINING_TYPES.REINFORCEMENT_LEARNING },
  { label: '大模型', value: TRAINING_TYPES.LARGE_MODEL }
]

// 状态选项
const statusOptions = Object.entries(STATUS_DISPLAY).map(([value, label]) => ({
  label,
  value
}))

/**
 * 获取训练类型显示文本
 */
const getTrainingTypeDisplay = (trainingType) => {
  const typeMap = {
    [TRAINING_TYPES.DEEP_LEARNING]: '深度学习',
    [TRAINING_TYPES.REINFORCEMENT_LEARNING]: '强化学习',
    [TRAINING_TYPES.LARGE_MODEL]: '大模型'
  }
  return typeMap[trainingType] || trainingType
}

/**
 * 获取训练类型颜色
 */
const getTrainingTypeColor = (trainingType) => {
  const colorMap = {
    [TRAINING_TYPES.DEEP_LEARNING]: 'blue',
    [TRAINING_TYPES.REINFORCEMENT_LEARNING]: 'green',
    [TRAINING_TYPES.LARGE_MODEL]: 'purple'
  }
  return colorMap[trainingType] || 'grey'
}

/**
 * 获取状态颜色
 */
const getStatusColor = (status) => {
  const colorMap = {
    [WORKFLOW_STATUS.DRAFT]: 'grey',
    [WORKFLOW_STATUS.STEP1_SAVED]: 'blue',
    [WORKFLOW_STATUS.STEP2_SAVED]: 'blue',
    [WORKFLOW_STATUS.TRAINING_READY]: 'orange',
    [WORKFLOW_STATUS.TRAINING]: 'green',
    [WORKFLOW_STATUS.PAUSED]: 'orange',
    [WORKFLOW_STATUS.TRAINING_COMPLETED]: 'positive',
    [WORKFLOW_STATUS.COMPLETED]: 'positive',
    [WORKFLOW_STATUS.FAILED]: 'negative',
    [WORKFLOW_STATUS.CANCELLED]: 'grey'
  }
  return colorMap[status] || 'grey'
}

/**
 * 获取训练状态颜色
 */
const getTrainingStatusColor = (trainingStatus) => {
  const colorMap = {
    [TRAINING_STATUS.RUNNING]: 'green',
    [TRAINING_STATUS.PAUSED]: 'orange',
    [TRAINING_STATUS.STOPPED]: 'grey',
    [TRAINING_STATUS.COMPLETED]: 'positive',
    [TRAINING_STATUS.FAILED]: 'negative'
  }
  return colorMap[trainingStatus] || 'grey'
}

/**
 * 格式化日期
 */
const formatDate = (dateString) => {
  return date.formatDate(dateString, 'MM-DD HH:mm')
}

/**
 * 获取任务列表
 */
const fetchTasks = async () => {
  try {
    loading.value = true
    
    const params = {
      page: currentPage.value,
      page_size: pageSize.value
    }
    
    if (filters.value.trainingType) {
      params.training_type = filters.value.trainingType
    }
    
    if (filters.value.status) {
      params.status = filters.value.status
    }
    
    const result = await workflowStore.fetchWorkflowList(params)
    
    taskList.value = result.workflows
    totalTasks.value = result.total
    
  } catch (error) {
    console.error('获取任务列表失败:', error)
  } finally {
    loading.value = false
  }
}

/**
 * 处理任务点击
 */
const handleTaskClick = async (task) => {
  try {
    loading.value = true
    
    // 跳转到对应步骤
    const jumpResult = await workflowStore.jumpToStep(task.task_id)
    
    // 获取目标路由
    const targetRoute = getPageRoute(jumpResult.training_type, jumpResult.target_step)
    
    if (targetRoute) {
      // 触发选中事件
      emit('task-selected', task)
      
      // 跳转到目标页面
      await router.push(targetRoute)
    }
    
  } catch (error) {
    console.error('跳转任务失败:', error)
  } finally {
    loading.value = false
  }
}

// 组件挂载时获取任务列表
onMounted(() => {
  fetchTasks()
})

// 暴露方法
defineExpose({
  fetchTasks,
  refresh: fetchTasks
})
</script>

<style scoped>
.workflow-task-list {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.filter-section {
  border-bottom: 1px solid #e0e0e0;
}

.task-list-container {
  flex: 1;
  overflow-y: auto;
}

.selected-task {
  background-color: #e3f2fd;
}

.task-name {
  font-weight: 500;
  font-size: 14px;
}

.task-info {
  font-size: 12px;
}

.empty-state,
.loading-state {
  color: #666;
}

.pagination-section {
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: center;
}
</style>
