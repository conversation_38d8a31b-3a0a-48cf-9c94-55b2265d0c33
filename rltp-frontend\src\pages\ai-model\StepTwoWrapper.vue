<template>
  <WorkflowStepWrapper
    :training-type="TRAINING_TYPES.DEEP_LEARNING"
    :step-number="2"
    :need-save="true"
  >
    <template #default="{ loading, onNextStep, onPrevStep }">
      <StepTwo 
        @next-step="handleNextStep"
        @prev-step="onPrevStep"
        :loading="loading"
      />
    </template>
  </WorkflowStepWrapper>
</template>

<script setup>
import { ref } from 'vue'
import WorkflowStepWrapper from 'src/components/WorkflowStepWrapper.vue'
import StepTwo from './StepTwo.vue'
import { TRAINING_TYPES } from 'src/services/workflowApi.js'
import { useModelFormStore } from 'src/stores/modelFormStore.js'

const modelFormStore = useModelFormStore()

/**
 * 处理下一步事件
 * 将原来的 modelFormStore 数据转换为新的工作流格式
 */
const handleNextStep = async (onNextStep) => {
  try {
    // 获取第二步的数据
    const stepTwoData = modelFormStore.stepTwoData
    
    // 转换为工作流格式
    const workflowData = {
      training_params: stepTwoData.params || {},
      resource_config: stepTwoData.resources || {},
      advanced_config: stepTwoData.advanced || {}
    }
    
    // 调用工作流的下一步方法
    await onNextStep(workflowData)
    
  } catch (error) {
    console.error('第二步保存失败:', error)
  }
}
</script>
