# Generated migration for RLTrainingModel

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('backend_api', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='RLTrainingModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('model_name', models.CharField(max_length=255, verbose_name='模型名称')),
                ('model_path', models.CharField(blank=True, max_length=1024, null=True, verbose_name='模型路径')),
                ('checkpoint_path', models.CharField(blank=True, max_length=1024, null=True, verbose_name='检查点路径')),
                ('best_episode_reward', models.FloatField(default=0.0, verbose_name='最佳回合奖励')),
                ('average_episode_reward', models.FloatField(default=0.0, verbose_name='平均回合奖励')),
                ('final_policy_loss', models.FloatField(blank=True, null=True, verbose_name='最终策略损失')),
                ('final_value_loss', models.FloatField(blank=True, null=True, verbose_name='最终价值损失')),
                ('final_entropy', models.FloatField(blank=True, null=True, verbose_name='最终熵值')),
                ('training_iterations', models.IntegerField(default=0, verbose_name='训练迭代次数')),
                ('total_episodes', models.IntegerField(default=0, verbose_name='总回合数')),
                ('convergence_episode', models.IntegerField(blank=True, null=True, verbose_name='收敛回合')),
                ('model_size_mb', models.FloatField(default=0.0, verbose_name='模型大小(MB)')),
                ('checkpoint_size_mb', models.FloatField(default=0.0, verbose_name='检查点大小(MB)')),
                ('algorithm_type', models.CharField(default='PPO', max_length=20, verbose_name='算法类型')),
                ('framework', models.CharField(default='Ray RLlib', max_length=50, verbose_name='框架')),
                ('status', models.CharField(choices=[('training', '训练中'), ('completed', '已完成'), ('failed', '失败'), ('exported', '已导出')], default='training', max_length=20, verbose_name='状态')),
                ('is_best', models.BooleanField(default=False, verbose_name='是否为最佳模型')),
                ('exported_formats', models.JSONField(blank=True, default=list, help_text='已导出的模型格式列表', verbose_name='导出格式')),
                ('export_paths', models.JSONField(blank=True, default=dict, help_text='各种格式的导出路径', verbose_name='导出路径')),
                ('metadata', models.JSONField(blank=True, default=dict, help_text='额外的模型信息', verbose_name='元数据')),
                ('created_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('completed_time', models.DateTimeField(blank=True, null=True, verbose_name='完成时间')),
                ('task', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='models', to='backend_api.rltrainingtask')),
            ],
            options={
                'verbose_name': '强化学习训练模型',
                'verbose_name_plural': '强化学习训练模型',
                'ordering': ['-created_time'],
            },
        ),
        migrations.AlterUniqueTogether(
            name='rltrainingmodel',
            unique_together={('task', 'model_name')},
        ),
    ]
