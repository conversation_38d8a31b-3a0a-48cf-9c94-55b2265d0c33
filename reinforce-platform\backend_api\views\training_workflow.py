from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.generics import ListCreateAPIView, RetrieveUpdateDestroyAPIView
from django.shortcuts import get_object_or_404
from django.db.models import Count, Q
from django.utils import timezone
from datetime import timedelta
import logging

from backend_api.models.training_workflow import TrainingWorkflow, TrainingMetrics, ModelEvaluation, InferenceModel
from backend_api.serializers.training_workflow import (
    TrainingWorkflowSerializer, TrainingWorkflowCreateSerializer, TrainingWorkflowListSerializer,
    WorkflowStepDataSerializer, TrainingMetricsSerializer, TrainingMetricsCreateSerializer,
    ModelEvaluationSerializer, ModelEvaluationCreateSerializer,
    InferenceModelSerializer, InferenceModelCreateSerializer,
    WorkflowOverviewSerializer, TrainingStartSerializer, TrainingStatusSerializer
)

logger = logging.getLogger(__name__)


class TrainingWorkflowListView(ListCreateAPIView):
    """训练工作流列表和创建视图"""
    
    permission_classes = [permissions.IsAuthenticated]
    
    def get_serializer_class(self):
        if self.request.method == 'POST':
            return TrainingWorkflowCreateSerializer
        return TrainingWorkflowListSerializer
    
    def get_queryset(self):
        """获取当前用户的工作流"""
        return TrainingWorkflow.objects.filter(created_by=self.request.user)
    
    def list(self, request, *args, **kwargs):
        """获取工作流列表"""
        queryset = self.get_queryset()
        
        # 支持按状态筛选
        status_filter = request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        # 支持按任务类型筛选
        task_type_filter = request.query_params.get('task_type')
        if task_type_filter:
            queryset = queryset.filter(task_type=task_type_filter)

        # 支持按训练类型筛选
        training_type_filter = request.query_params.get('training_type')
        if training_type_filter:
            queryset = queryset.filter(training_type=training_type_filter)
        
        # 分页
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(queryset, many=True)
        return Response({
            'results': serializer.data,
            'count': queryset.count()
        })
    
    def perform_create(self, serializer):
        """创建工作流时设置创建者"""
        serializer.save(created_by=self.request.user)


class TrainingWorkflowDetailView(RetrieveUpdateDestroyAPIView):
    """训练工作流详情视图"""
    
    serializer_class = TrainingWorkflowSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return TrainingWorkflow.objects.filter(created_by=self.request.user)


class WorkflowStepNavigationView(APIView):
    """工作流步骤导航视图"""
    
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        """导航到下一步"""
        workflow_id = request.data.get('workflow_id')
        target_step = request.data.get('target_step')
        
        workflow = get_object_or_404(
            TrainingWorkflow, 
            id=workflow_id, 
            created_by=request.user
        )
        
        # 检查是否可以进入目标步骤
        if target_step and workflow.can_proceed_to_next_step():
            workflow.current_step = target_step
            workflow.save()
            
            return Response({
                'success': True,
                'current_step': workflow.current_step,
                'message': f'已进入{workflow.get_current_step_display()}'
            })
        
        return Response({
            'success': False,
            'message': '无法进入该步骤，请完成当前步骤的必要配置'
        }, status=status.HTTP_400_BAD_REQUEST)


class WorkflowStepDataView(APIView):
    """获取和更新特定步骤的数据"""

    permission_classes = [permissions.AllowAny]
    
    def get(self, request, workflow_id, step):
        """获取指定步骤的数据"""
        try:
            workflow = TrainingWorkflow.objects.get(id=workflow_id)
        except TrainingWorkflow.DoesNotExist:
            # 如果工作流不存在，返回默认数据
            return Response({
                'workflow_id': workflow_id,
                'step': step,
                'data': {},
                'current_step': 'draft',
                'can_proceed': True
            })
        
        step_data = {}
        if step == 'step1_data':
            step_data = workflow.step1_config
        elif step == 'step2_model':
            step_data = workflow.step2_config
        elif step == 'step3_training':
            step_data = workflow.step3_config
        elif step == 'step4_evaluation':
            step_data = workflow.step4_config
        elif step == 'step5_deployment':
            step_data = workflow.step5_config
        
        return Response({
            'workflow_id': workflow.id,
            'step': step,
            'data': step_data,
            'current_step': workflow.current_step,
            'can_proceed': workflow.can_proceed_to_next_step()
        })
    
    def post(self, request, workflow_id, step):
        """保存指定步骤的数据"""
        try:
            workflow = TrainingWorkflow.objects.get(id=workflow_id)
        except TrainingWorkflow.DoesNotExist:
            # 如果工作流不存在，创建一个新的
            from user.models import User
            try:
                user = User.objects.first()  # 获取第一个用户
                if not user:
                    # 如果没有用户，创建一个默认用户
                    user = User.objects.create_user(
                        username='default_user',
                        email='<EMAIL>',
                        password='defaultpass'
                    )
            except:
                # 如果用户模型有问题，使用None
                user = None

            workflow = TrainingWorkflow.objects.create(
                id=workflow_id,
                name=f'工作流-{workflow_id}',
                description='自动创建的工作流',
                task_type='object_detection_yolov8',
                created_by=user
            )
        
        serializer = WorkflowStepDataSerializer(data=request.data)
        if serializer.is_valid():
            step_data = serializer.validated_data['data']
            
            # 保存步骤数据
            if step == 'step1_data':
                workflow.step1_config = step_data
            elif step == 'step2_model':
                workflow.step2_config = step_data
            elif step == 'step3_training':
                workflow.step3_config = step_data
            elif step == 'step4_evaluation':
                workflow.step4_config = step_data
            elif step == 'step5_deployment':
                workflow.step5_config = step_data
            
            # 更新当前步骤
            step_mapping = {
                'step1_data': 'step1_data',
                'step2_model': 'step2_model',
                'step3_training': 'step3_training',
                'step4_evaluation': 'step4_evaluation',
                'step5_deployment': 'step5_deployment'
            }
            
            if step in step_mapping:
                workflow.current_step = step_mapping[step]
                workflow.updated_at = timezone.now()
            
            workflow.save()
            
            return Response({
                'success': True,
                'message': '数据保存成功',
                'current_step': workflow.current_step,
                'can_proceed': workflow.can_proceed_to_next_step()
            })
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class TrainingMetricsView(APIView):
    """训练指标视图"""
    
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request, workflow_id):
        """获取训练指标"""
        workflow = get_object_or_404(TrainingWorkflow, id=workflow_id, created_by=request.user)
        
        metrics = TrainingMetrics.objects.filter(workflow=workflow).order_by('epoch', 'step')
        serializer = TrainingMetricsSerializer(metrics, many=True)
        
        return Response({
            'workflow_id': workflow_id,
            'metrics': serializer.data,
            'total_records': metrics.count()
        })
    
    def post(self, request, workflow_id):
        """添加训练指标"""
        workflow = get_object_or_404(TrainingWorkflow, id=workflow_id, created_by=request.user)
        
        serializer = TrainingMetricsCreateSerializer(data=request.data)
        if serializer.is_valid():
            metrics = serializer.save(workflow=workflow)
            
            # 更新工作流的最佳指标
            if metrics.map50_95 and (not workflow.best_metrics.get('map50_95') or 
                                   metrics.map50_95 > workflow.best_metrics.get('map50_95', 0)):
                workflow.best_metrics = {
                    'map50_95': metrics.map50_95,
                    'map50': metrics.map50,
                    'precision': metrics.precision,
                    'recall': metrics.recall,
                    'epoch': metrics.epoch
                }
                workflow.save()
            
            return Response({
                'success': True,
                'message': '指标记录成功',
                'metrics': TrainingMetricsSerializer(metrics).data
            }, status=status.HTTP_201_CREATED)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ModelEvaluationView(APIView):
    """模型评估视图"""
    
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request, workflow_id):
        """获取模型评估结果"""
        workflow = get_object_or_404(TrainingWorkflow, id=workflow_id, created_by=request.user)
        
        evaluations = ModelEvaluation.objects.filter(workflow=workflow).order_by('-created_at')
        serializer = ModelEvaluationSerializer(evaluations, many=True)
        
        return Response({
            'workflow_id': workflow_id,
            'evaluations': serializer.data
        })
    
    def post(self, request, workflow_id):
        """创建模型评估任务"""
        workflow = get_object_or_404(TrainingWorkflow, id=workflow_id, created_by=request.user)
        
        serializer = ModelEvaluationCreateSerializer(data=request.data)
        if serializer.is_valid():
            evaluation = serializer.save(workflow=workflow)
            
            # 这里可以启动异步评估任务
            # start_model_evaluation_task.delay(evaluation.id)
            
            return Response({
                'success': True,
                'message': '评估任务已创建',
                'evaluation': ModelEvaluationSerializer(evaluation).data
            }, status=status.HTTP_201_CREATED)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class InferenceModelListView(ListCreateAPIView):
    """推理模型列表视图"""
    
    permission_classes = [permissions.IsAuthenticated]
    
    def get_serializer_class(self):
        if self.request.method == 'POST':
            return InferenceModelCreateSerializer
        return InferenceModelSerializer
    
    def get_queryset(self):
        """获取当前用户的推理模型"""
        return InferenceModel.objects.filter(workflow__created_by=self.request.user)
    
    def perform_create(self, serializer):
        """创建推理模型"""
        workflow_id = self.request.data.get('workflow')
        workflow = get_object_or_404(TrainingWorkflow, id=workflow_id, created_by=self.request.user)
        serializer.save(workflow=workflow)


class InferenceModelDetailView(RetrieveUpdateDestroyAPIView):
    """推理模型详情视图"""
    
    serializer_class = InferenceModelSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return InferenceModel.objects.filter(workflow__created_by=self.request.user)


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def workflow_overview(request):
    """工作流概览"""
    try:
        user = request.user

        # 统计数据
        total_workflows = TrainingWorkflow.objects.filter(created_by=user).count()
        active_workflows = TrainingWorkflow.objects.filter(
            created_by=user,
            status__in=['configuring', 'training', 'evaluating']
        ).count()
        completed_workflows = TrainingWorkflow.objects.filter(
            created_by=user,
            status='completed'
        ).count()
        failed_workflows = TrainingWorkflow.objects.filter(
            created_by=user,
            status='failed'
        ).count()

        # 任务类型统计
        task_types = TrainingWorkflow.objects.filter(created_by=user).values('task_type').annotate(
            count=Count('id')
        ).order_by('-count')

        # 最近的工作流
        recent_workflows = TrainingWorkflow.objects.filter(created_by=user).order_by('-created_at')[:10]

        # 训练统计
        training_stats = {
            'total_training_time': 0,  # 可以从TrainingMetrics计算
            'average_accuracy': 0,     # 可以从最佳指标计算
            'models_deployed': InferenceModel.objects.filter(
                workflow__created_by=user,
                status='deployed'
            ).count()
        }

        data = {
            'total_workflows': total_workflows,
            'active_workflows': active_workflows,
            'completed_workflows': completed_workflows,
            'failed_workflows': failed_workflows,
            'task_types': list(task_types),
            'recent_workflows': TrainingWorkflowListSerializer(recent_workflows, many=True).data,
            'training_stats': training_stats
        }

        return Response(data)

    except Exception as e:
        logger.error(f"工作流概览API错误: {str(e)}")
        return Response({
            'error': str(e),
            'total_workflows': 0,
            'active_workflows': 0,
            'completed_workflows': 0,
            'failed_workflows': 0,
            'task_types': [],
            'recent_workflows': [],
            'training_stats': {
                'total_training_time': 0,
                'average_accuracy': 0,
                'models_deployed': 0
            }
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class TrainingStartView(APIView):
    """启动训练任务视图"""

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        """启动训练任务"""
        workflow_id = request.data.get('workflow_id')
        training_config = request.data.get('training_config', {})

        workflow = get_object_or_404(TrainingWorkflow, id=workflow_id, created_by=request.user)

        try:
            # 更新工作流状态
            workflow.status = 'training'
            workflow.started_at = timezone.now()
            workflow.step3_config = training_config
            workflow.save()

            # 这里可以集成实际的训练启动逻辑
            # 例如调用现有的训练API或启动训练任务

            # 模拟创建训练任务ID
            training_task_id = 12345  # 实际应该从训练系统获取
            workflow.training_task_id = training_task_id
            workflow.save()

            return Response({
                'success': True,
                'message': '训练任务已启动',
                'training_task_id': training_task_id,
                'workflow_id': workflow_id
            })

        except Exception as error:
            return Response({
                'success': False,
                'message': f'启动训练失败: {str(error)}'
            }, status=status.HTTP_400_BAD_REQUEST)


class TrainingStatusView(APIView):
    """获取训练状态视图"""

    permission_classes = [permissions.IsAuthenticated]

    def get(self, request, workflow_id):
        """获取训练状态"""
        workflow = get_object_or_404(TrainingWorkflow, id=workflow_id, created_by=request.user)

        # 获取最新的训练指标
        latest_metrics = TrainingMetrics.objects.filter(workflow=workflow).order_by('-recorded_at').first()

        # 计算进度
        if workflow.step3_config.get('epochs'):
            total_epochs = workflow.step3_config['epochs']
            current_epoch = latest_metrics.epoch if latest_metrics else 0
            progress = (current_epoch / total_epochs) * 100
        else:
            progress = 0

        return Response({
            'workflow_id': workflow_id,
            'status': workflow.status,
            'progress': progress,
            'current_epoch': latest_metrics.epoch if latest_metrics else 0,
            'total_epochs': workflow.step3_config.get('epochs', 0),
            'latest_metrics': TrainingMetricsSerializer(latest_metrics).data if latest_metrics else None
        })


class WorkflowCreateView(APIView):
    """创建训练工作流视图 - 点击开始训练时调用"""

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        """创建新的训练工作流并创建模型信息记录"""
        try:
            data = request.data
            training_type = data.get('training_type', 'deep_learning')

            # 创建工作流
            workflow = TrainingWorkflow.objects.create(
                name=data.get('name', ''),
                description=data.get('description', ''),
                training_type=training_type,
                task_type=data.get('task_type', ''),
                created_by=request.user
            )

            # 创建模型信息记录
            from backend_api.models.training_model import TrainingModel
            model_info = TrainingModel.objects.create(
                model_name=data.get('model_name', workflow.name),
                task_id=workflow.task_id,  # 使用workflow的task_id
                created_by=request.user
            )

            # 关联模型信息ID
            workflow.model_info_id = model_info.id
            workflow.save()

            return Response({
                'success': True,
                'message': '训练任务创建成功',
                'task_id': workflow.task_id,
                'workflow_id': workflow.id,
                'model_info_id': model_info.id,
                'training_type': workflow.training_type,
                'current_step': workflow.current_step,
                'status': workflow.status
            }, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error(f"创建训练任务失败: {str(e)}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)


class WorkflowStepSaveView(APIView):
    """保存工作流步骤数据视图 - 前两步的下一步按钮调用"""

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        """保存步骤数据并更新状态"""
        try:
            data = request.data
            task_id = data.get('task_id')
            step_number = data.get('step_number')  # 1, 2
            step_data = data.get('step_data', {})

            # 获取工作流
            workflow = get_object_or_404(TrainingWorkflow, task_id=task_id, created_by=request.user)

            # 更新步骤配置
            workflow.update_step_config(step_number, step_data)

            # 更新状态和当前步骤
            if step_number == 1:
                workflow.status = 'step1_saved'
                workflow.current_step = 2  # 进入第二步
            elif step_number == 2:
                workflow.status = 'step2_saved'
                workflow.current_step = 3  # 进入第三步

            workflow.save()

            return Response({
                'success': True,
                'message': f'步骤{step_number}数据保存成功',
                'current_step': workflow.current_step,
                'status': workflow.status,
                'next_step_number': workflow.get_next_step_number()
            })

        except Exception as e:
            logger.error(f"保存步骤数据失败: {str(e)}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)


class WorkflowDataAnalysisView(APIView):
    """数据分析视图 - 第一步点击开始分析调用"""

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        """开始数据分析"""
        try:
            data = request.data
            task_id = data.get('task_id')
            dataset_config = data.get('dataset_config', {})

            # 获取工作流
            workflow = get_object_or_404(TrainingWorkflow, task_id=task_id, created_by=request.user)

            # 这里可以调用实际的数据分析接口
            # 模拟数据分析过程
            analysis_result = {
                'dataset_id': dataset_config.get('dataset_id'),
                'dataset_name': dataset_config.get('dataset_name'),
                'total_samples': dataset_config.get('total_samples', 0),
                'train_samples': int(dataset_config.get('total_samples', 0) * dataset_config.get('train_ratio', 0.8)),
                'val_samples': int(dataset_config.get('total_samples', 0) * dataset_config.get('val_ratio', 0.2)),
                'train_ratio': dataset_config.get('train_ratio', 0.8),
                'val_ratio': dataset_config.get('val_ratio', 0.2),
                'classes': dataset_config.get('classes', []),
                'analysis_completed': True,
                'analysis_time': timezone.now().isoformat()
            }

            # 更新步骤1配置
            workflow.update_step_config(1, analysis_result)

            return Response({
                'success': True,
                'message': '数据分析完成',
                'analysis_result': analysis_result
            })

        except Exception as e:
            logger.error(f"数据分析失败: {str(e)}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)


class WorkflowTrainingSubmitView(APIView):
    """提交训练视图 - 第三步点击开始训练调用"""

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        """提交模型训练"""
        try:
            data = request.data
            task_id = data.get('task_id')
            training_config = data.get('training_config', {})

            # 获取工作流
            workflow = get_object_or_404(TrainingWorkflow, task_id=task_id, created_by=request.user)

            # 保存第三步的基础信息配置
            workflow.update_step_config(3, training_config)

            # 创建训练任务记录（调用现有的TrainingTask）
            from backend_api.models.training import TrainingTask
            training_task = TrainingTask.objects.create(
                algorithm_version=training_config.get('algorithm_version', 'v8'),
                model_path=training_config.get('model_path', 'yolov8n.pt'),
                dataset_id=workflow.step1_config.get('dataset_id', 1),
                # 其他训练参数从step2_config获取
                **workflow.step2_config
            )

            # 关联训练任务ID
            workflow.training_task_id = training_task.id
            workflow.status = 'training'
            workflow.training_status = 'running'
            workflow.training_started_at = timezone.now()
            workflow.save()

            # 这里可以调用实际的训练启动接口
            # 例如：start_training_task(training_task.id)

            return Response({
                'success': True,
                'message': '训练任务已开始',
                'training_task_id': training_task.id,
                'status': workflow.status,
                'training_status': workflow.training_status
            })

        except Exception as e:
            logger.error(f"提交训练失败: {str(e)}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)


class WorkflowTrainingControlView(APIView):
    """训练控制视图（暂停/恢复/停止）- 第三步训练页面的控制按钮调用"""

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        """控制训练状态"""
        try:
            data = request.data
            task_id = data.get('task_id')
            action = data.get('action')  # pause, resume, stop

            # 获取工作流
            workflow = get_object_or_404(TrainingWorkflow, task_id=task_id, created_by=request.user)

            # 更新训练状态
            if action == 'pause':
                workflow.status = 'paused'
                workflow.training_status = 'paused'
                message = '训练已暂停'
            elif action == 'resume':
                workflow.status = 'training'
                workflow.training_status = 'running'
                message = '训练已恢复'
            elif action == 'stop':
                workflow.status = 'cancelled'
                workflow.training_status = 'stopped'
                message = '训练已停止'
            else:
                return Response({
                    'success': False,
                    'error': '无效的操作'
                }, status=status.HTTP_400_BAD_REQUEST)

            workflow.save()

            # 这里可以调用实际的训练控制接口
            # 例如：control_training_task(workflow.training_task_id, action)

            return Response({
                'success': True,
                'message': message,
                'status': workflow.status,
                'training_status': workflow.training_status
            })

        except Exception as e:
            logger.error(f"控制训练失败: {str(e)}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)


class WorkflowListView(APIView):
    """获取所有训练任务列表视图 - 右侧任务列表调用"""

    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        """获取训练任务列表"""
        try:
            # 获取查询参数
            training_type = request.query_params.get('training_type')
            status_filter = request.query_params.get('status')
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('page_size', 10))

            # 构建查询
            queryset = TrainingWorkflow.objects.filter(created_by=request.user)

            if training_type:
                queryset = queryset.filter(training_type=training_type)
            if status_filter:
                queryset = queryset.filter(status=status_filter)

            # 分页
            total = queryset.count()
            start = (page - 1) * page_size
            end = start + page_size
            workflows = queryset.order_by('-created_at')[start:end]

            # 序列化数据
            workflow_list = []
            for workflow in workflows:
                workflow_data = {
                    'task_id': workflow.task_id,
                    'name': workflow.name,
                    'training_type': workflow.training_type,
                    'training_type_display': workflow.get_training_type_display(),
                    'task_type': workflow.task_type,
                    'status': workflow.status,
                    'status_display': workflow.get_status_display(),
                    'training_status': workflow.training_status,
                    'training_status_display': workflow.get_training_status_display(),
                    'current_step': workflow.current_step,
                    'step_name': workflow.get_step_name(),
                    'progress_percentage': workflow.get_progress_percentage(),
                    'created_at': workflow.created_at,
                    'updated_at': workflow.updated_at,
                    'training_started_at': workflow.training_started_at,
                    'can_proceed': workflow.can_proceed_to_next_step(),
                    'next_step_number': workflow.get_next_step_number()
                }
                workflow_list.append(workflow_data)

            return Response({
                'success': True,
                'data': {
                    'total': total,
                    'page': page,
                    'page_size': page_size,
                    'workflows': workflow_list
                }
            })

        except Exception as e:
            logger.error(f"获取工作流列表失败: {str(e)}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class WorkflowJumpToStepView(APIView):
    """跳转到指定步骤视图 - 从右侧列表点击跳转调用"""

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        """从右侧列表跳转到保存好的步骤"""
        try:
            data = request.data
            task_id = data.get('task_id')

            # 获取工作流
            workflow = get_object_or_404(TrainingWorkflow, task_id=task_id, created_by=request.user)

            # 获取应该跳转到的页面和步骤
            target_page, target_step = workflow.get_target_page_for_jump()

            # 获取对应步骤的数据
            step_data = workflow.get_step_config(target_step)

            # 如果是第三步，还需要返回训练状态和指标
            training_data = {}
            if target_step == 3:
                training_data = {
                    'training_status': workflow.training_status,
                    'training_metrics': workflow.training_metrics,
                    'training_logs': workflow.training_logs[-10:] if workflow.training_logs else [],  # 最近10条日志
                    'training_task_id': workflow.training_task_id,
                    'training_started_at': workflow.training_started_at
                }

            return Response({
                'success': True,
                'data': {
                    'task_id': workflow.task_id,
                    'workflow_id': workflow.id,
                    'name': workflow.name,
                    'training_type': workflow.training_type,
                    'task_type': workflow.task_type,
                    'current_step': workflow.current_step,
                    'target_step': target_step,
                    'target_page': target_page,
                    'step_name': workflow.get_step_name(),
                    'step_data': step_data,
                    'status': workflow.status,
                    'training_status': workflow.training_status,
                    'progress_percentage': workflow.get_progress_percentage(),
                    'can_proceed': workflow.can_proceed_to_next_step(),
                    'next_step_number': workflow.get_next_step_number(),
                    'training_data': training_data
                }
            })

        except Exception as e:
            logger.error(f"跳转到步骤失败: {str(e)}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)


class WorkflowTrainingMetricsUpdateView(APIView):
    """更新训练过程指标视图 - 训练过程中实时更新指标和日志"""

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        """更新训练过程中的指标数据和日志"""
        try:
            data = request.data
            task_id = data.get('task_id')
            metrics_data = data.get('metrics', {})
            log_entry = data.get('log_entry')

            # 获取工作流
            workflow = get_object_or_404(TrainingWorkflow, task_id=task_id, created_by=request.user)

            # 更新训练指标
            if metrics_data:
                workflow.update_training_metrics(metrics_data)

            # 添加日志条目
            if log_entry:
                workflow.add_training_log(log_entry)

            # 同时创建TrainingMetrics记录用于历史追踪
            if metrics_data.get('epoch') is not None:
                TrainingMetrics.objects.create(
                    workflow=workflow,
                    epoch=metrics_data.get('epoch', 0),
                    step=metrics_data.get('step', 0),
                    train_loss=metrics_data.get('train_loss'),
                    val_loss=metrics_data.get('val_loss'),
                    train_accuracy=metrics_data.get('train_accuracy'),
                    val_accuracy=metrics_data.get('val_accuracy'),
                    precision=metrics_data.get('precision'),
                    recall=metrics_data.get('recall'),
                    map50=metrics_data.get('map50'),
                    map50_95=metrics_data.get('map50_95'),
                    learning_rate=metrics_data.get('learning_rate'),
                    gpu_memory=metrics_data.get('gpu_memory'),
                    training_time=metrics_data.get('training_time'),
                    additional_metrics=metrics_data.get('additional_metrics', {})
                )

            return Response({
                'success': True,
                'message': '训练数据更新成功',
                'current_metrics': workflow.training_metrics,
                'latest_logs': workflow.training_logs[-5:] if workflow.training_logs else []
            })

        except Exception as e:
            logger.error(f"更新训练数据失败: {str(e)}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)


class WorkflowStepCompleteView(APIView):
    """完成步骤视图 - 第三步点击下一步时调用"""

    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        """完成当前步骤，保存训练结果并进入下一步"""
        try:
            data = request.data
            task_id = data.get('task_id')
            step_number = data.get('step_number')
            final_metrics = data.get('final_metrics', {})

            # 获取工作流
            workflow = get_object_or_404(TrainingWorkflow, task_id=task_id, created_by=request.user)

            if step_number == 3:
                # 第三步完成，保存最终训练结果
                workflow.training_status = 'completed'
                workflow.status = 'training_completed'
                workflow.training_completed_at = timezone.now()

                # 保存最终指标
                if final_metrics:
                    workflow.update_training_metrics(final_metrics)

                # 进入下一步
                workflow.current_step = 4
                workflow.save()

                return Response({
                    'success': True,
                    'message': '训练完成，进入评估阶段',
                    'current_step': workflow.current_step,
                    'status': workflow.status,
                    'training_status': workflow.training_status
                })

            else:
                return Response({
                    'success': False,
                    'error': '无效的步骤号'
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"完成步骤失败: {str(e)}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)
