# 训练工作流API文档

## 概述

新的训练工作流系统支持三种类型的训练：
- **深度学习** (deep_learning): 数据加载分割 → 训练参数配备 → 提交模型训练 → 训练结果评估 → 模型导出存储
- **强化学习** (reinforcement_learning): 交互环境接入 → 训练参数配置 → 提交模型训练 → 训练结果评估 → 模型导出存储  
- **大模型** (large_model): 数据加载分割 → 训练参数配备 → 提交微调训练 → 训练结果评估 → 模型量化剪枝 → 模型部署推理

## API接口

### 1. 创建训练工作流

**POST** `/backend/workflows/create/`

```json
{
  "name": "我的训练任务",
  "description": "任务描述",
  "training_type": "deep_learning",  // deep_learning, reinforcement_learning, large_model
  "task_type": "object_detection_yolov8"  // 根据training_type选择对应的任务类型
}
```

**响应:**
```json
{
  "success": true,
  "message": "训练工作流创建成功",
  "task_id": "uuid-string",
  "workflow_id": 123,
  "training_type": "deep_learning"
}
```

### 2. 开始数据分析

**POST** `/backend/workflows/data/analysis/`

```json
{
  "task_id": "uuid-string",
  "dataset_config": {
    "dataset_id": 1,
    "total_samples": 1000,
    "classes": ["class1", "class2"]
  }
}
```

### 3. 保存工作流步骤数据

**POST** `/backend/workflows/step/save/`

```json
{
  "task_id": "uuid-string",
  "step_id": "step1",  // step1, step2, step3, step4, step5, step6
  "step_data": {
    // 步骤相关的配置数据
  }
}
```

### 4. 提交模型训练

**POST** `/backend/workflows/training/submit/`

```json
{
  "task_id": "uuid-string",
  "training_config": {
    "epochs": 100,
    "batch_size": 16,
    "learning_rate": 0.001
  }
}
```

### 5. 训练控制（暂停/恢复/停止）

**POST** `/backend/workflows/training/control/`

```json
{
  "task_id": "uuid-string",
  "action": "pause"  // pause, resume, stop
}
```

### 6. 获取训练任务列表

**GET** `/backend/workflows/list/`

**查询参数:**
- `training_type`: 训练类型筛选
- `status`: 状态筛选
- `page`: 页码
- `page_size`: 每页数量

### 7. 跳转到指定步骤

**POST** `/backend/workflows/jump/`

```json
{
  "task_id": "uuid-string"
}
```

**响应:**
```json
{
  "success": true,
  "data": {
    "task_id": "uuid-string",
    "workflow_id": 123,
    "name": "任务名称",
    "training_type": "deep_learning",
    "current_step": "step2_training_config",
    "target_page": "training_config",
    "step_data": {},
    "status": "configuring",
    "progress_percentage": 40,
    "can_proceed": true,
    "next_step": "step3_model_training"
  }
}
```

### 8. 更新训练过程指标

**POST** `/backend/workflows/metrics/update/`

```json
{
  "task_id": "uuid-string",
  "metrics": {
    "epoch": 10,
    "step": 100,
    "train_loss": 0.5,
    "val_loss": 0.6,
    "train_accuracy": 0.85,
    "val_accuracy": 0.82
  }
}
```

## 数据库模型

### TrainingWorkflow 主要字段

- `task_id`: 唯一任务标识符 (UUID)
- `training_type`: 训练类型 (deep_learning/reinforcement_learning/large_model)
- `task_type`: 具体任务类型
- `current_step`: 当前步骤
- `status`: 任务状态 (draft/configuring/training/paused/evaluating/completed/failed/cancelled)
- `step1_config` ~ `step6_config`: 各步骤配置数据 (JSON)
- `training_metrics`: 训练过程指标 (JSON)
- `training_logs`: 训练日志 (JSON Array)

## 使用流程

1. **创建工作流**: 调用创建接口，获得task_id
2. **配置步骤**: 根据训练类型，依次配置各个步骤
3. **提交训练**: 提交训练任务，系统创建training_task_id
4. **监控训练**: 通过WebSocket或轮询获取训练状态和指标
5. **控制训练**: 可以暂停、恢复或停止训练
6. **完成流程**: 训练完成后进行评估和模型导出

## 前端集成

前端可以通过右侧任务列表点击跳转到对应的步骤页面，系统会自动加载保存的配置数据。
