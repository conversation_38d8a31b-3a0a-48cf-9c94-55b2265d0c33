<template>
  <div class="workflow-step-wrapper">
    <!-- 无工作流提示 -->
    <div v-if="!currentTaskId" class="no-workflow-warning">
      <q-banner class="bg-warning text-white">
        <template v-slot:avatar>
          <q-icon name="warning" color="white" />
        </template>
        <div class="text-h6">未找到训练任务</div>
        <div class="q-mt-sm">
          请返回主页面点击"开始训练"按钮创建新的训练任务，然后再进行数据分析。
        </div>
        <template v-slot:action>
          <q-btn
            flat
            color="white"
            label="返回主页"
            @click="$router.push('/training-workflow')"
          />
        </template>
      </q-banner>
    </div>

    <!-- 步骤内容插槽 -->
    <slot
      v-else
      :current-workflow="currentWorkflow"
      :step-data="stepData"
      :loading="loading"
      :on-data-analysis="handleDataAnalysis"
      :on-next-step="handleNextStep"
      :on-prev-step="handlePrevStep"
      :on-training-submit="handleTrainingSubmit"
      :on-training-control="handleTrainingControl"
      :on-step-complete="handleStepComplete"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useWorkflowStore } from 'src/stores/workflowStore'
import { getPageRoute, TRAINING_TYPES } from 'src/services/workflowApi'

const props = defineProps({
  stepNumber: {
    type: Number,
    required: true
  },
  trainingType: {
    type: String,
    required: true
  },
  // 是否需要保存步骤数据 (前三步为true，后面步骤为false)
  needSave: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['step-data-changed', 'step-completed'])

const router = useRouter()
const route = useRoute()
const workflowStore = useWorkflowStore()

// 当前步骤数据
const stepData = ref({})
const loading = ref(false)

// 计算属性
const currentWorkflow = computed(() => workflowStore.currentWorkflow)
const currentTaskId = computed(() => workflowStore.currentTaskId)

/**
 * 处理数据分析 (第一步)
 */
const handleDataAnalysis = async (datasetConfig) => {
  try {
    // 检查是否有当前工作流
    if (!currentTaskId.value) {
      throw new Error('请先创建训练任务。请返回主页面点击"开始训练"按钮创建新任务。')
    }

    loading.value = true
    const result = await workflowStore.startDataAnalysis(datasetConfig)

    // 更新步骤数据
    stepData.value = { ...stepData.value, ...result }
    emit('step-data-changed', stepData.value)

    return result
  } catch (error) {
    console.error('数据分析失败:', error)
    throw error
  } finally {
    loading.value = false
  }
}

/**
 * 处理下一步按钮点击
 */
const handleNextStep = async (currentStepData = {}) => {
  try {
    // 检查是否有当前工作流
    if (!currentTaskId.value) {
      throw new Error('请先创建训练任务。请返回主页面点击"开始训练"按钮创建新任务。')
    }

    loading.value = true

    // 更新步骤数据
    stepData.value = { ...stepData.value, ...currentStepData }

    // 前三步需要保存数据
    if (props.needSave && props.stepNumber <= 3) {
      if (props.stepNumber <= 2) {
        // 前两步：保存数据并跳转到下一步
        const result = await workflowStore.saveStepData(props.stepNumber, stepData.value)

        // 跳转到下一步
        const nextStep = result.current_step
        const nextRoute = getPageRoute(props.trainingType, nextStep)

        if (nextRoute) {
          await router.push(nextRoute)
        }
      } else if (props.stepNumber === 3) {
        // 第三步：完成训练步骤
        await handleStepComplete(currentStepData)
      }
    } else {
      // 后面步骤：直接跳转，不保存
      const nextStep = props.stepNumber + 1
      const nextRoute = getPageRoute(props.trainingType, nextStep)

      if (nextRoute) {
        await router.push(nextRoute)
      }
    }
    
    emit('step-completed', {
      stepNumber: props.stepNumber,
      stepData: stepData.value
    })
    
  } catch (error) {
    console.error('下一步操作失败:', error)
  } finally {
    loading.value = false
  }
}

/**
 * 处理上一步按钮点击
 */
const handlePrevStep = () => {
  const prevStep = props.stepNumber - 1
  if (prevStep >= 1) {
    const prevRoute = getPageRoute(props.trainingType, prevStep)
    if (prevRoute) {
      router.push(prevRoute)
    }
  }
}

/**
 * 处理训练提交 (第三步开始训练)
 */
const handleTrainingSubmit = async (trainingConfig) => {
  try {
    loading.value = true
    const result = await workflowStore.submitTraining(trainingConfig)
    
    // 更新步骤数据
    stepData.value = { ...stepData.value, ...trainingConfig }
    emit('step-data-changed', stepData.value)
    
    return result
  } catch (error) {
    console.error('提交训练失败:', error)
    throw error
  } finally {
    loading.value = false
  }
}

/**
 * 处理训练控制 (第三步的暂停/恢复/停止)
 */
const handleTrainingControl = async (action) => {
  try {
    loading.value = true
    const result = await workflowStore.controlTraining(action)
    return result
  } catch (error) {
    console.error('训练控制失败:', error)
    throw error
  } finally {
    loading.value = false
  }
}

/**
 * 处理步骤完成 (第三步的下一步)
 */
const handleStepComplete = async (finalMetrics = {}) => {
  try {
    loading.value = true
    const result = await workflowStore.completeStep(props.stepNumber, finalMetrics)
    
    // 跳转到下一步
    const nextStep = result.current_step
    const nextRoute = getPageRoute(props.trainingType, nextStep)
    
    if (nextRoute) {
      await router.push(nextRoute)
    }
    
    return result
  } catch (error) {
    console.error('完成步骤失败:', error)
    throw error
  } finally {
    loading.value = false
  }
}

/**
 * 从路由参数或store加载步骤数据
 */
const loadStepData = () => {
  if (currentWorkflow.value) {
    // 从当前工作流获取步骤数据
    const stepDataKey = `step${props.stepNumber}_config`
    if (currentWorkflow.value[stepDataKey]) {
      stepData.value = { ...currentWorkflow.value[stepDataKey] }
    }

    // 如果是第三步，还需要加载训练数据
    if (props.stepNumber === 3 && currentWorkflow.value.training_data) {
      stepData.value = {
        ...stepData.value,
        ...currentWorkflow.value.training_data
      }
    }
  }
}

/**
 * 监听当前工作流变化
 */
watch(currentWorkflow, (newWorkflow) => {
  if (newWorkflow) {
    loadStepData()
  }
}, { immediate: true })

/**
 * 组件挂载时加载数据
 */
onMounted(() => {
  loadStepData()
})

// 暴露方法给父组件
defineExpose({
  stepData,
  loading,
  handleDataAnalysis,
  handleNextStep,
  handlePrevStep,
  handleTrainingSubmit,
  handleTrainingControl,
  handleStepComplete
})
</script>

<style scoped>
.workflow-step-wrapper {
  width: 100%;
  height: 100%;
}
</style>
