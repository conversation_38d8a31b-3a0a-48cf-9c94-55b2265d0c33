# Generated manually for training workflow updates

from django.db import migrations, models
import uuid


def generate_task_ids(apps, schema_editor):
    """为现有的工作流生成task_id"""
    TrainingWorkflow = apps.get_model('backend_api', 'TrainingWorkflow')
    for workflow in TrainingWorkflow.objects.all():
        if not workflow.task_id:
            workflow.task_id = str(uuid.uuid4())
            workflow.save()


def migrate_step_configs(apps, schema_editor):
    """迁移旧的步骤配置到新的字段"""
    TrainingWorkflow = apps.get_model('backend_api', 'TrainingWorkflow')
    for workflow in TrainingWorkflow.objects.all():
        # 迁移旧字段到新字段
        if hasattr(workflow, 'step1_data_config'):
            workflow.step1_config = workflow.step1_data_config
        if hasattr(workflow, 'step2_model_config'):
            workflow.step2_config = workflow.step2_model_config
        if hasattr(workflow, 'step3_training_config'):
            workflow.step3_config = workflow.step3_training_config
        if hasattr(workflow, 'step4_evaluation_config'):
            workflow.step4_config = workflow.step4_evaluation_config
        if hasattr(workflow, 'step5_deployment_config'):
            workflow.step5_config = workflow.step5_deployment_config
        workflow.save()


class Migration(migrations.Migration):

    dependencies = [
        ('backend_api', '0003_add_rl_training_model'),
    ]

    operations = [
        # 添加新字段
        migrations.AddField(
            model_name='trainingworkflow',
            name='task_id',
            field=models.CharField(default='', help_text='唯一任务标识符', max_length=100, unique=True, verbose_name='任务ID'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='trainingworkflow',
            name='training_type',
            field=models.CharField(choices=[('deep_learning', '深度学习'), ('reinforcement_learning', '强化学习'), ('large_model', '大模型')], default='deep_learning', max_length=30, verbose_name='训练类型'),
        ),
        migrations.AddField(
            model_name='trainingworkflow',
            name='step1_config',
            field=models.JSONField(blank=True, default=dict, help_text='数据加载分割/交互环境接入', verbose_name='步骤1配置'),
        ),
        migrations.AddField(
            model_name='trainingworkflow',
            name='step2_config',
            field=models.JSONField(blank=True, default=dict, help_text='训练参数配备/配置', verbose_name='步骤2配置'),
        ),
        migrations.AddField(
            model_name='trainingworkflow',
            name='step3_config',
            field=models.JSONField(blank=True, default=dict, help_text='模型训练/微调训练', verbose_name='步骤3配置'),
        ),
        migrations.AddField(
            model_name='trainingworkflow',
            name='step4_config',
            field=models.JSONField(blank=True, default=dict, help_text='训练结果评估', verbose_name='步骤4配置'),
        ),
        migrations.AddField(
            model_name='trainingworkflow',
            name='step5_config',
            field=models.JSONField(blank=True, default=dict, help_text='模型导出存储/量化剪枝', verbose_name='步骤5配置'),
        ),
        migrations.AddField(
            model_name='trainingworkflow',
            name='step6_config',
            field=models.JSONField(blank=True, default=dict, help_text='模型部署推理(仅大模型)', verbose_name='步骤6配置'),
        ),
        migrations.AddField(
            model_name='trainingworkflow',
            name='training_metrics',
            field=models.JSONField(blank=True, default=dict, help_text='实时训练指标数据', verbose_name='训练过程指标'),
        ),
        migrations.AddField(
            model_name='trainingworkflow',
            name='training_logs',
            field=models.JSONField(blank=True, default=list, help_text='训练过程日志', verbose_name='训练日志'),
        ),
        
        # 运行数据迁移
        migrations.RunPython(generate_task_ids),
        migrations.RunPython(migrate_step_configs),
        
        # 修改现有字段
        migrations.AlterField(
            model_name='trainingworkflow',
            name='task_type',
            field=models.CharField(blank=True, help_text='根据训练类型动态选择', max_length=50, verbose_name='任务类型'),
        ),
        migrations.AlterField(
            model_name='trainingworkflow',
            name='current_step',
            field=models.CharField(default='draft', help_text='根据训练类型动态选择步骤', max_length=30, verbose_name='当前步骤'),
        ),
        
        # 添加暂停状态
        migrations.AlterField(
            model_name='trainingworkflow',
            name='status',
            field=models.CharField(choices=[('draft', '草稿'), ('configuring', '配置中'), ('training', '训练中'), ('paused', '暂停中'), ('evaluating', '评估中'), ('completed', '已完成'), ('failed', '失败'), ('cancelled', '已取消')], default='draft', max_length=20, verbose_name='任务状态'),
        ),
    ]
