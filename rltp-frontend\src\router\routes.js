const routes = [
  {
    path: '/login',
    meta: { notRequireAuth: true },
    component: () => import('src/pages/user/LoginPage.vue'),
  },
  // {
  //   path: '/user',
  //   component: () => import('layouts/MainLayout.vue'),
  //   children: [
  //     // {
  //     //   path: '',
  //     //   component: () => import('pages/user/IndexPage.vue'),
  //     // },
  //     {
  //       path: 'register',
  //       component: () => import('pages/user/RegisterPage.vue'),
  //     },
  //   ],
  // },
  {
    path: '/user/register',
    meta: { notRequireAuth: true },
    component: () => import('src/pages/user/RegisterPage.vue'),
  },
  {
    path: '/user/forgot-password',
    meta: { notRequireAuth: true },
    component: () => import('src/pages/user/ForgotPasswordPage.vue'),
  },
  {
    path: '/',
    redirect: '/intelligent-algorithm',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      {
        path: 'intelligent-algorithm',
        component: () => import('pages/algorithm/IntelligentAlgorithmPage.vue'),
      },
      {
        path: 'overview',
        component: () => import('pages/overview/IndexPage.vue'),
      },
      {
        path: 'emulation',
        component: () => import('pages/emulation/IndexPage.vue'),
      },
      {
        path: 'storage',
        component: () => import('pages/storage/IndexPage.vue'),
      },
    ],
  },

  {
    path: '/training',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      {
        path: '',
        component: () => import('pages/training/IndexPage.vue'),
      },
      {
        path: 'detail/:id(\\d+)',
        component: () => import('pages/training/DetailPage.vue'),
        props: true,
      },
    ],
  },

  {
    path: '/algorithm',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      {
        path: '',
        component: () => import('pages/algorithm/IndexPage.vue'),
      },
      {
        path: 'create',
        component: () => import('pages/algorithm/DetailPage.vue'),
      },
      {
        path: 'detail/:id(\\d+)',
        component: () => import('pages/algorithm/DetailPage.vue'),
        props: true,
      },
    ],
  },

  {
    path: '/model',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      {
        path: '',
        component: () => import('pages/model/IndexPage.vue'),
      },
      {
        path: 'detail/:id(\\d+)',
        component: () => import('pages/model/DetailPage.vue'),
        props: true,
      },
      {
        path: 'video/:id(\\d+)',
        component: () => import('pages/model/VideoPage.vue'),
        props: true,
      },
    ],
  },

  {
    path: '/ai-model',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      // 深度学习
      {
        path: 'intelligent',
        component: () => import('src/pages/ai-model/IntelligentModelPage.vue'),
      },
      {
        path: 'startTrain',
        component: () => import('src/pages/ai-model/StepForm.vue'),
      },
      // 强化学习👇
      {
        path: 'reinforcementStudy',
        component: () => import('src/pages/reinforcementLearing/HomePage.vue'),
      },
      {
        path: 'reinforcementStudyForm',
        component: () => import('src/pages/reinforcementLearing/reinforceForm.vue'),
      },
      // 大模型👇
      {
        path: 'largeHome',
        component: () => import('src/pages/large-model/HomePage.vue'),
      },
      {
        path: 'largeModelForm',
        component: () => import('src/pages/large-model/LargeForm.vue'),
      },
      // 文本数据👇
      {
        path: 'textHome',
        component: () => import('src/pages/text-data/HomePage.vue'),
      },
      {
        path: 'textModelForm',
        component: () => import('src/pages/text-data/TextForm.vue'),
      },
      // end
      {
        path: 'reinforcement',
        component: () => import('src/pages/ai-model/ReinforcementLearningPage.vue'),
      },
      {
        path: 'deep-learning',
        component: () => import('src/pages/ai-model/DeepLearningPage.vue'),
      },
    ],
  },

  {
    path: '/ai-interaction',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      {
        path: '',
        component: () => import('pages/ai-interaction/IndexPage.vue'),
      },
    ],
  },

  {
    path: '/system',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      {
        path: '',
        component: () => import('src/pages/system/IndexPage.vue'),
      },
      {
        path: 'users',
        component: () => import('src/pages/system/UserManagementPage.vue'),
      },
      {
        path: 'simulation',
        component: () => import('src/pages/system/SimulationManagementPage.vue'),
      },
      {
        path: 'model',
        component: () => import('src/pages/system/ModelManagementPage.vue'),
        name: 'model-management',
        meta: { title: '模型管理' },
      },
      {
        path: 'dataset',
        component: () => import('pages/system/DatasetManagementPage.vue'),
        name: 'dataset-management',
        meta: { title: '数据集管理' },
      },
      {
        path: 'config',
        component: () => import('pages/system/ConfigEnv.vue'),
        name: 'config-management',
        meta: { title: '配置环境管理' },
      },
      {
        path: 'server',
        component: () => import('pages/system/ServerManagement.vue'),
        name: 'server-management',
        meta: { title: '服务器运维管理' },
      },
      {
        path: 'test',
        component: () => import('pages/system/DataProcessOne.vue'),
        name: 'test-management',
        meta: { title: 'xx' },
      },
    ],
  },
  // 统一训练工作流路由
  {
    path: '/training-workflow',
    component: () => import('layouts/MainLayout.vue'),
    children: [
      // 深度学习路由
      {
        path: 'deep-learning',
        children: [
          {
            path: 'step1',
            name: 'DeepLearningStep1',
            component: () => import('src/pages/ai-model/StepOne.vue'),
            meta: {
              title: '数据加载分割',
              trainingType: 'deep_learning',
              stepNumber: 1,
              needSave: true
            }
          },
          {
            path: 'step2',
            name: 'DeepLearningStep2',
            component: () => import('src/pages/ai-model/StepTwo.vue'),
            meta: {
              title: '训练参数配备',
              trainingType: 'deep_learning',
              stepNumber: 2,
              needSave: true
            }
          },
          {
            path: 'step3',
            name: 'DeepLearningStep3',
            component: () => import('src/pages/ai-model/StepThree.vue'),
            meta: {
              title: '提交模型训练',
              trainingType: 'deep_learning',
              stepNumber: 3,
              needSave: true
            }
          },
          {
            path: 'step4',
            name: 'DeepLearningStep4',
            component: () => import('src/pages/ai-model/StepFour.vue'),
            meta: {
              title: '训练结果评估',
              trainingType: 'deep_learning',
              stepNumber: 4,
              needSave: false
            }
          },
          {
            path: 'step5',
            name: 'DeepLearningStep5',
            component: () => import('src/pages/ai-model/StepFive.vue'),
            meta: {
              title: '模型导出存储',
              trainingType: 'deep_learning',
              stepNumber: 5,
              needSave: false
            }
          }
        ]
      },

      // 强化学习路由
      {
        path: 'reinforcement-learning',
        children: [
          {
            path: 'step1',
            name: 'ReinforcementLearningStep1',
            component: () => import('src/pages/reinforcementLearing/StepOne.vue'),
            meta: {
              title: '交互环境接入',
              trainingType: 'reinforcement_learning',
              stepNumber: 1,
              needSave: true
            }
          },
          {
            path: 'step2',
            name: 'ReinforcementLearningStep2',
            component: () => import('src/pages/reinforcementLearing/StepTwo.vue'),
            meta: {
              title: '训练参数配置',
              trainingType: 'reinforcement_learning',
              stepNumber: 2,
              needSave: true
            }
          },
          {
            path: 'step3',
            name: 'ReinforcementLearningStep3',
            component: () => import('src/pages/reinforcementLearing/StepThree.vue'),
            meta: {
              title: '提交模型训练',
              trainingType: 'reinforcement_learning',
              stepNumber: 3,
              needSave: true
            }
          },
          {
            path: 'step4',
            name: 'ReinforcementLearningStep4',
            component: () => import('src/pages/reinforcementLearing/StepFour.vue'),
            meta: {
              title: '训练结果评估',
              trainingType: 'reinforcement_learning',
              stepNumber: 4,
              needSave: false
            }
          },
          {
            path: 'step5',
            name: 'ReinforcementLearningStep5',
            component: () => import('src/pages/reinforcementLearing/StepFive.vue'),
            meta: {
              title: '模型导出存储',
              trainingType: 'reinforcement_learning',
              stepNumber: 5,
              needSave: false
            }
          }
        ]
      },

      // 大模型路由
      {
        path: 'large-model',
        children: [
          {
            path: 'step1',
            name: 'LargeModelStep1',
            component: () => import('src/pages/large-model/StepOne.vue'),
            meta: {
              title: '数据加载分割',
              trainingType: 'large_model',
              stepNumber: 1,
              needSave: true
            }
          },
          {
            path: 'step2',
            name: 'LargeModelStep2',
            component: () => import('src/pages/large-model/StepTwo.vue'),
            meta: {
              title: '训练参数配置',
              trainingType: 'large_model',
              stepNumber: 2,
              needSave: true
            }
          },
          {
            path: 'step3',
            name: 'LargeModelStep3',
            component: () => import('src/pages/large-model/StepThree.vue'),
            meta: {
              title: '提交模型训练',
              trainingType: 'large_model',
              stepNumber: 3,
              needSave: true
            }
          },
          {
            path: 'step4',
            name: 'LargeModelStep4',
            component: () => import('src/pages/large-model/StepFour.vue'),
            meta: {
              title: '训练结果评估',
              trainingType: 'large_model',
              stepNumber: 4,
              needSave: false
            }
          },
          {
            path: 'step5',
            name: 'LargeModelStep5',
            component: () => import('src/pages/large-model/StepFive.vue'),
            meta: {
              title: '模型导出存储',
              trainingType: 'large_model',
              stepNumber: 5,
              needSave: false
            }
          }
        ]
      },

      // 文本数据路由
      {
        path: 'text-data',
        children: [
          {
            path: 'step1',
            name: 'TextDataStep1',
            component: () => import('src/pages/text-data/StepOne.vue'),
            meta: {
              title: '数据加载分割',
              trainingType: 'text_data',
              stepNumber: 1,
              needSave: true
            }
          },
          {
            path: 'step2',
            name: 'TextDataStep2',
            component: () => import('src/pages/text-data/StepTwo.vue'),
            meta: {
              title: '训练参数配置',
              trainingType: 'text_data',
              stepNumber: 2,
              needSave: true
            }
          },
          {
            path: 'step3',
            name: 'TextDataStep3',
            component: () => import('src/pages/text-data/StepThree.vue'),
            meta: {
              title: '提交模型训练',
              trainingType: 'text_data',
              stepNumber: 3,
              needSave: true
            }
          },
          {
            path: 'step4',
            name: 'TextDataStep4',
            component: () => import('src/pages/text-data/StepFour.vue'),
            meta: {
              title: '训练结果评估',
              trainingType: 'text_data',
              stepNumber: 4,
              needSave: false
            }
          },
          {
            path: 'step5',
            name: 'TextDataStep5',
            component: () => import('src/pages/text-data/StepFive.vue'),
            meta: {
              title: '模型导出存储',
              trainingType: 'text_data',
              stepNumber: 5,
              needSave: false
            }
          }
        ]
      }
    ]
  },

  {
    path: '/:catchAll(.*)*',
    component: () => import('pages/ErrorNotFound.vue'),
  },
]

export default routes
