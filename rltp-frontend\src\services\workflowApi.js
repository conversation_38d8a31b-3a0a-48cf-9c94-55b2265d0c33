import { api } from 'boot/axios'

/**
 * 统一训练工作流API服务
 */
export const workflowApi = {
  /**
   * 创建训练工作流 (点击开始训练时调用)
   * @param {Object} data - 创建参数
   * @param {string} data.name - 任务名称
   * @param {string} data.description - 任务描述
   * @param {string} data.training_type - 训练类型 (deep_learning/reinforcement_learning/large_model)
   * @param {string} data.task_type - 具体任务类型
   * @param {string} data.model_name - 模型名称
   */
  createWorkflow(data) {
    return api.post('/backend/workflows/create/', data)
  },

  /**
   * 开始数据分析 (第一步点击开始分析)
   * @param {Object} data - 分析参数
   * @param {string} data.task_id - 任务ID
   * @param {Object} data.dataset_config - 数据集配置
   */
  startDataAnalysis(data) {
    return api.post('/backend/workflows/data/analysis/', data)
  },

  /**
   * 保存步骤数据 (前两步点击下一步)
   * @param {Object} data - 保存参数
   * @param {string} data.task_id - 任务ID
   * @param {number} data.step_number - 步骤号 (1 或 2)
   * @param {Object} data.step_data - 步骤数据
   */
  saveStepData(data) {
    return api.post('/backend/workflows/step/save/', data)
  },

  /**
   * 提交模型训练 (第三步点击开始训练)
   * @param {Object} data - 训练参数
   * @param {string} data.task_id - 任务ID
   * @param {Object} data.training_config - 训练配置
   */
  submitTraining(data) {
    return api.post('/backend/workflows/training/submit/', data)
  },

  /**
   * 训练控制 (第三步的暂停/恢复/停止按钮)
   * @param {Object} data - 控制参数
   * @param {string} data.task_id - 任务ID
   * @param {string} data.action - 操作 (pause/resume/stop)
   */
  controlTraining(data) {
    return api.post('/backend/workflows/training/control/', data)
  },

  /**
   * 完成步骤 (第三步点击下一步)
   * @param {Object} data - 完成参数
   * @param {string} data.task_id - 任务ID
   * @param {number} data.step_number - 步骤号
   * @param {Object} data.final_metrics - 最终指标
   */
  completeStep(data) {
    return api.post('/backend/workflows/step/complete/', data)
  },

  /**
   * 获取训练任务列表 (右侧任务列表)
   * @param {Object} params - 查询参数
   * @param {string} params.training_type - 训练类型筛选
   * @param {string} params.status - 状态筛选
   * @param {number} params.page - 页码
   * @param {number} params.page_size - 每页数量
   */
  getWorkflowList(params = {}) {
    return api.get('/backend/workflows/list/', { params })
  },

  /**
   * 跳转到指定步骤 (从右侧列表点击跳转)
   * @param {Object} data - 跳转参数
   * @param {string} data.task_id - 任务ID
   */
  jumpToStep(data) {
    console.log('=== workflowApi.jumpToStep ===')
    console.log('请求数据:', data)
    console.log('请求URL: /backend/workflows/jump/')
    console.log('请求方法: POST')

    return api.post('/backend/workflows/jump/', data)
  },

  /**
   * 更新训练过程指标 (训练过程中实时调用)
   * @param {Object} data - 更新参数
   * @param {string} data.task_id - 任务ID
   * @param {Object} data.metrics - 训练指标
   * @param {string} data.log_entry - 日志条目
   */
  updateTrainingMetrics(data) {
    return api.post('/backend/workflows/metrics/update/', data)
  }
}

/**
 * 训练类型常量
 */
export const TRAINING_TYPES = {
  DEEP_LEARNING: 'deep_learning',
  REINFORCEMENT_LEARNING: 'reinforcement_learning', 
  LARGE_MODEL: 'large_model'
}

/**
 * 任务状态常量
 */
export const WORKFLOW_STATUS = {
  DRAFT: 'draft',
  STEP1_SAVED: 'step1_saved',
  STEP2_SAVED: 'step2_saved',
  TRAINING_READY: 'training_ready',
  TRAINING: 'training',
  PAUSED: 'paused',
  TRAINING_COMPLETED: 'training_completed',
  EVALUATION_COMPLETED: 'evaluation_completed',
  EXPORT_COMPLETED: 'export_completed',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled'
}

/**
 * 训练状态常量
 */
export const TRAINING_STATUS = {
  NOT_STARTED: 'not_started',
  RUNNING: 'running',
  PAUSED: 'paused',
  STOPPED: 'stopped',
  COMPLETED: 'completed',
  FAILED: 'failed'
}

/**
 * 状态显示映射
 */
export const STATUS_DISPLAY = {
  [WORKFLOW_STATUS.DRAFT]: '草稿',
  [WORKFLOW_STATUS.STEP1_SAVED]: '第一步已保存',
  [WORKFLOW_STATUS.STEP2_SAVED]: '第二步已保存',
  [WORKFLOW_STATUS.TRAINING_READY]: '准备训练',
  [WORKFLOW_STATUS.TRAINING]: '训练中',
  [WORKFLOW_STATUS.PAUSED]: '已暂停',
  [WORKFLOW_STATUS.TRAINING_COMPLETED]: '训练完成',
  [WORKFLOW_STATUS.EVALUATION_COMPLETED]: '评估完成',
  [WORKFLOW_STATUS.EXPORT_COMPLETED]: '导出完成',
  [WORKFLOW_STATUS.COMPLETED]: '全部完成',
  [WORKFLOW_STATUS.FAILED]: '失败',
  [WORKFLOW_STATUS.CANCELLED]: '已取消'
}

export const TRAINING_STATUS_DISPLAY = {
  [TRAINING_STATUS.NOT_STARTED]: '未开始',
  [TRAINING_STATUS.RUNNING]: '运行中',
  [TRAINING_STATUS.PAUSED]: '已暂停',
  [TRAINING_STATUS.STOPPED]: '已停止',
  [TRAINING_STATUS.COMPLETED]: '已完成',
  [TRAINING_STATUS.FAILED]: '训练失败'
}

/**
 * 根据训练类型获取步骤名称
 */
export const getStepName = (trainingType, stepNumber) => {
  const stepNames = {
    [TRAINING_TYPES.DEEP_LEARNING]: {
      1: '数据加载分割',
      2: '训练参数配备',
      3: '提交模型训练',
      4: '训练结果评估',
      5: '模型导出存储'
    },
    [TRAINING_TYPES.REINFORCEMENT_LEARNING]: {
      1: '交互环境接入',
      2: '训练参数配置',
      3: '提交模型训练',
      4: '训练结果评估',
      5: '模型导出存储'
    },
    [TRAINING_TYPES.LARGE_MODEL]: {
      1: '数据加载分割',
      2: '训练参数配备',
      3: '提交微调训练',
      4: '训练结果评估',
      5: '模型量化剪枝',
      6: '模型部署推理'
    }
  }
  return stepNames[trainingType]?.[stepNumber] || `步骤${stepNumber}`
}

/**
 * 根据训练类型获取最大步骤数
 */
export const getMaxSteps = (trainingType) => {
  const maxSteps = {
    [TRAINING_TYPES.DEEP_LEARNING]: 5,
    [TRAINING_TYPES.REINFORCEMENT_LEARNING]: 5,
    [TRAINING_TYPES.LARGE_MODEL]: 6
  }
  return maxSteps[trainingType] || 5
}

/**
 * 根据训练类型和目标步骤获取页面路由
 */
export const getPageRoute = (trainingType, targetStep) => {
  const routes = {
    [TRAINING_TYPES.DEEP_LEARNING]: {
      1: '/training-workflow/deep-learning/step1',
      2: '/training-workflow/deep-learning/step2',
      3: '/training-workflow/deep-learning/step3',
      4: '/training-workflow/deep-learning/step4',
      5: '/training-workflow/deep-learning/step5'
    },
    [TRAINING_TYPES.REINFORCEMENT_LEARNING]: {
      1: '/training-workflow/reinforcement-learning/step1',
      2: '/training-workflow/reinforcement-learning/step2',
      3: '/training-workflow/reinforcement-learning/step3',
      4: '/training-workflow/reinforcement-learning/step4',
      5: '/training-workflow/reinforcement-learning/step5'
    },
    [TRAINING_TYPES.LARGE_MODEL]: {
      1: '/training-workflow/large-model/step1',
      2: '/training-workflow/large-model/step2',
      3: '/training-workflow/large-model/step3',
      4: '/training-workflow/large-model/step4',
      5: '/training-workflow/large-model/step5',
      6: '/training-workflow/large-model/step6'
    },
    [TRAINING_TYPES.TEXT_DATA]: {
      1: '/training-workflow/text-data/step1',
      2: '/training-workflow/text-data/step2',
      3: '/training-workflow/text-data/step3',
      4: '/training-workflow/text-data/step4',
      5: '/training-workflow/text-data/step5'
    }
  }
  return routes[trainingType]?.[targetStep] || '/'
}
