#!/usr/bin/env python
"""
测试统一训练工作流API的脚本
"""

import requests
import json
import time

# API基础URL
BASE_URL = "http://localhost:8000/backend"

# 测试用户认证token (需要替换为实际的token)
AUTH_TOKEN = "your_auth_token_here"

headers = {
    "Authorization": f"Bearer {AUTH_TOKEN}",
    "Content-Type": "application/json"
}

def test_create_workflow():
    """测试创建训练工作流"""
    print("=== 测试创建训练工作流 ===")
    
    data = {
        "name": "测试深度学习任务",
        "description": "这是一个测试任务",
        "training_type": "deep_learning",
        "task_type": "object_detection_yolov8",
        "model_name": "YOLOv8测试模型"
    }
    
    response = requests.post(f"{BASE_URL}/workflows/create/", 
                           json=data, headers=headers)
    
    if response.status_code == 201:
        result = response.json()
        print(f"✅ 创建成功: task_id = {result['task_id']}")
        return result['task_id']
    else:
        print(f"❌ 创建失败: {response.text}")
        return None

def test_data_analysis(task_id):
    """测试数据分析"""
    print("=== 测试数据分析 ===")
    
    data = {
        "task_id": task_id,
        "dataset_config": {
            "dataset_id": 1,
            "dataset_name": "COCO测试数据集",
            "total_samples": 1000,
            "train_ratio": 0.8,
            "val_ratio": 0.2,
            "classes": ["person", "car", "dog"]
        }
    }
    
    response = requests.post(f"{BASE_URL}/workflows/data/analysis/", 
                           json=data, headers=headers)
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ 数据分析成功: {result['message']}")
        return True
    else:
        print(f"❌ 数据分析失败: {response.text}")
        return False

def test_save_step(task_id, step_number, step_data):
    """测试保存步骤数据"""
    print(f"=== 测试保存步骤{step_number} ===")
    
    data = {
        "task_id": task_id,
        "step_number": step_number,
        "step_data": step_data
    }
    
    response = requests.post(f"{BASE_URL}/workflows/step/save/", 
                           json=data, headers=headers)
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ 步骤{step_number}保存成功: {result['message']}")
        return True
    else:
        print(f"❌ 步骤{step_number}保存失败: {response.text}")
        return False

def test_submit_training(task_id):
    """测试提交训练"""
    print("=== 测试提交训练 ===")
    
    data = {
        "task_id": task_id,
        "training_config": {
            "algorithm_version": "v8",
            "model_path": "yolov8n.pt",
            "epochs": 10,
            "batch_size": 16,
            "learning_rate": 0.001
        }
    }
    
    response = requests.post(f"{BASE_URL}/workflows/training/submit/", 
                           json=data, headers=headers)
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ 训练提交成功: training_task_id = {result['training_task_id']}")
        return result['training_task_id']
    else:
        print(f"❌ 训练提交失败: {response.text}")
        return None

def test_training_control(task_id, action):
    """测试训练控制"""
    print(f"=== 测试训练控制: {action} ===")
    
    data = {
        "task_id": task_id,
        "action": action
    }
    
    response = requests.post(f"{BASE_URL}/workflows/training/control/", 
                           json=data, headers=headers)
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ 训练控制成功: {result['message']}")
        return True
    else:
        print(f"❌ 训练控制失败: {response.text}")
        return False

def test_get_workflow_list():
    """测试获取工作流列表"""
    print("=== 测试获取工作流列表 ===")
    
    response = requests.get(f"{BASE_URL}/workflows/list/", headers=headers)
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ 获取列表成功: 共{result['data']['total']}个任务")
        return result['data']['workflows']
    else:
        print(f"❌ 获取列表失败: {response.text}")
        return []

def test_jump_to_step(task_id):
    """测试跳转到步骤"""
    print("=== 测试跳转到步骤 ===")
    
    data = {
        "task_id": task_id
    }
    
    response = requests.post(f"{BASE_URL}/workflows/jump/", 
                           json=data, headers=headers)
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ 跳转成功: 目标步骤 = {result['data']['target_step']}")
        return result['data']
    else:
        print(f"❌ 跳转失败: {response.text}")
        return None

def main():
    """主测试流程"""
    print("开始测试统一训练工作流API...")
    
    # 1. 创建工作流
    task_id = test_create_workflow()
    if not task_id:
        return
    
    # 2. 数据分析
    if not test_data_analysis(task_id):
        return
    
    # 3. 保存步骤1
    step1_data = {
        "dataset_id": 1,
        "train_samples": 800,
        "val_samples": 200
    }
    if not test_save_step(task_id, 1, step1_data):
        return
    
    # 4. 保存步骤2
    step2_data = {
        "epochs": 10,
        "batch_size": 16,
        "learning_rate": 0.001,
        "optimizer": "Adam"
    }
    if not test_save_step(task_id, 2, step2_data):
        return
    
    # 5. 提交训练
    training_task_id = test_submit_training(task_id)
    if not training_task_id:
        return
    
    # 6. 测试训练控制
    test_training_control(task_id, "pause")
    time.sleep(1)
    test_training_control(task_id, "resume")
    
    # 7. 获取工作流列表
    workflows = test_get_workflow_list()
    
    # 8. 测试跳转
    test_jump_to_step(task_id)
    
    print("\n🎉 所有测试完成!")

if __name__ == "__main__":
    main()
