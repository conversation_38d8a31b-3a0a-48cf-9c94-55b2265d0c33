<template>
  <WorkflowStepWrapper
    :training-type="TRAINING_TYPES.DEEP_LEARNING"
    :step-number="3"
    :need-save="false"
  >
    <template #default="{ loading, onTrainingSubmit, onTrainingControl, onStepComplete }">
      <StepThree 
        @training-submit="handleTrainingSubmit"
        @training-control="onTrainingControl"
        @step-complete="onStepComplete"
        @next-step="onStepComplete"
        :loading="loading"
      />
    </template>
  </WorkflowStepWrapper>
</template>

<script setup>
import { ref } from 'vue'
import WorkflowStepWrapper from 'src/components/WorkflowStepWrapper.vue'
import StepThree from './StepThree.vue'
import { TRAINING_TYPES } from 'src/services/workflowApi.js'
import { useModelFormStore } from 'src/stores/modelFormStore.js'

const modelFormStore = useModelFormStore()

/**
 * 处理训练提交事件
 * 将原来的 modelFormStore 数据转换为新的工作流格式
 */
const handleTrainingSubmit = async (onTrainingSubmit) => {
  try {
    // 获取第三步的数据
    const stepThreeData = modelFormStore.stepThreeData
    
    // 转换为工作流格式
    const trainingConfig = {
      training_name: stepThreeData.trainingName || '',
      description: stepThreeData.description || '',
      training_params: stepThreeData.params || {},
      resource_allocation: stepThreeData.resources || {}
    }
    
    // 调用工作流的训练提交方法
    await onTrainingSubmit(trainingConfig)
    
  } catch (error) {
    console.error('训练提交失败:', error)
  }
}
</script>
