from django.db import models
from django.conf import settings
from django.utils import timezone
import uuid


class TrainingWorkflow(models.Model):
    """训练工作流模型 - 支持深度学习、强化学习、大模型三种类型"""

    # 训练类型选择
    TRAINING_TYPE_CHOICES = [
        ('deep_learning', '深度学习'),
        ('reinforcement_learning', '强化学习'),
        ('large_model', '大模型'),
    ]

    # 深度学习任务类型
    DL_TASK_TYPE_CHOICES = [
        ('object_detection_yolov8', '目标识别-YoloV8'),
        ('object_detection_yolov5', '目标识别-YoloV5'),
        ('image_classification', '图像分类'),
        ('semantic_segmentation', '语义分割'),
        ('instance_segmentation', '实例分割'),
    ]

    # 强化学习任务类型
    RL_TASK_TYPE_CHOICES = [
        ('dqn', 'DQN'),
        ('ppo', 'PPO'),
        ('a3c', 'A3C'),
        ('ddpg', 'DDPG'),
        ('sac', 'SAC'),
    ]

    # 大模型任务类型
    LM_TASK_TYPE_CHOICES = [
        ('llm_fine_tuning', 'LLM微调'),
        ('text_generation', '文本生成'),
        ('text_classification', '文本分类'),
        ('question_answering', '问答系统'),
        ('text_summarization', '文本摘要'),
    ]

    # 深度学习步骤
    DL_STEP_CHOICES = [
        ('draft', '草稿'),
        ('step1_data_loading', '步骤1-数据加载分割'),
        ('step2_training_config', '步骤2-训练参数配备'),
        ('step3_model_training', '步骤3-提交模型训练'),
        ('step4_result_evaluation', '步骤4-训练结果评估'),
        ('step5_model_export', '步骤5-模型导出存储'),
        ('completed', '已完成'),
        ('failed', '失败'),
    ]

    # 强化学习步骤
    RL_STEP_CHOICES = [
        ('draft', '草稿'),
        ('step1_env_setup', '步骤1-交互环境接入'),
        ('step2_training_config', '步骤2-训练参数配置'),
        ('step3_model_training', '步骤3-提交模型训练'),
        ('step4_result_evaluation', '步骤4-训练结果评估'),
        ('step5_model_export', '步骤5-模型导出存储'),
        ('completed', '已完成'),
        ('failed', '失败'),
    ]

    # 大模型步骤
    LM_STEP_CHOICES = [
        ('draft', '草稿'),
        ('step1_data_loading', '步骤1-数据加载分割'),
        ('step2_training_config', '步骤2-训练参数配备'),
        ('step3_fine_tuning', '步骤3-提交微调训练'),
        ('step4_result_evaluation', '步骤4-训练结果评估'),
        ('step5_model_quantization', '步骤5-模型量化剪枝'),
        ('step6_model_deployment', '步骤6-模型部署推理'),
        ('completed', '已完成'),
        ('failed', '失败'),
    ]
    
    STATUS_CHOICES = [
        ('draft', '草稿'),
        ('configuring', '配置中'),
        ('training', '训练中'),
        ('paused', '暂停中'),
        ('evaluating', '评估中'),
        ('completed', '已完成'),
        ('failed', '失败'),
        ('cancelled', '已取消'),
    ]

    # 基本信息
    id = models.AutoField(primary_key=True)
    task_id = models.CharField('任务ID', max_length=100, unique=True, help_text='唯一任务标识符')
    name = models.CharField('任务名称', max_length=200)
    description = models.TextField('任务描述', blank=True)

    # 训练类型和任务类型
    training_type = models.CharField('训练类型', max_length=30, choices=TRAINING_TYPE_CHOICES, default='deep_learning')
    task_type = models.CharField('任务类型', max_length=50, blank=True, help_text='根据训练类型动态选择')

    # 状态管理
    current_step = models.CharField('当前步骤', max_length=30, default='draft', help_text='根据训练类型动态选择步骤')
    status = models.CharField('任务状态', max_length=20, choices=STATUS_CHOICES, default='draft')
    
    # 时间信息
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    started_at = models.DateTimeField('开始训练时间', null=True, blank=True)
    completed_at = models.DateTimeField('完成时间', null=True, blank=True)
    
    # 步骤配置数据 - 使用通用字段存储不同类型的配置
    step1_config = models.JSONField('步骤1配置', default=dict, blank=True, help_text='数据加载分割/交互环境接入')
    step2_config = models.JSONField('步骤2配置', default=dict, blank=True, help_text='训练参数配备/配置')
    step3_config = models.JSONField('步骤3配置', default=dict, blank=True, help_text='模型训练/微调训练')
    step4_config = models.JSONField('步骤4配置', default=dict, blank=True, help_text='训练结果评估')
    step5_config = models.JSONField('步骤5配置', default=dict, blank=True, help_text='模型导出存储/量化剪枝')
    step6_config = models.JSONField('步骤6配置', default=dict, blank=True, help_text='模型部署推理(仅大模型)')

    # 训练过程数据
    training_metrics = models.JSONField('训练过程指标', default=dict, blank=True, help_text='实时训练指标数据')
    training_logs = models.JSONField('训练日志', default=list, blank=True, help_text='训练过程日志')
    
    # 关联信息
    training_task_id = models.IntegerField('关联的训练任务ID', null=True, blank=True)
    model_path = models.CharField('训练完成的模型路径', max_length=500, blank=True)
    best_metrics = models.JSONField('最佳训练指标', default=dict, blank=True)
    
    # 用户关联
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, verbose_name='创建者')
    
    class Meta:
        verbose_name = '训练工作流'
        verbose_name_plural = '训练工作流'
        db_table = 'training_workflow'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.name} ({self.get_training_type_display()})"

    def save(self, *args, **kwargs):
        """保存时自动生成task_id"""
        if not self.task_id:
            import uuid
            self.task_id = str(uuid.uuid4())
        super().save(*args, **kwargs)

    def get_step_choices(self):
        """根据训练类型获取步骤选择"""
        if self.training_type == 'deep_learning':
            return self.DL_STEP_CHOICES
        elif self.training_type == 'reinforcement_learning':
            return self.RL_STEP_CHOICES
        elif self.training_type == 'large_model':
            return self.LM_STEP_CHOICES
        return self.DL_STEP_CHOICES

    def get_task_type_choices(self):
        """根据训练类型获取任务类型选择"""
        if self.training_type == 'deep_learning':
            return self.DL_TASK_TYPE_CHOICES
        elif self.training_type == 'reinforcement_learning':
            return self.RL_TASK_TYPE_CHOICES
        elif self.training_type == 'large_model':
            return self.LM_TASK_TYPE_CHOICES
        return self.DL_TASK_TYPE_CHOICES

    def get_progress_percentage(self):
        """获取进度百分比"""
        step_orders = {
            'deep_learning': ['draft', 'step1_data_loading', 'step2_training_config', 'step3_model_training', 'step4_result_evaluation', 'step5_model_export', 'completed'],
            'reinforcement_learning': ['draft', 'step1_env_setup', 'step2_training_config', 'step3_model_training', 'step4_result_evaluation', 'step5_model_export', 'completed'],
            'large_model': ['draft', 'step1_data_loading', 'step2_training_config', 'step3_fine_tuning', 'step4_result_evaluation', 'step5_model_quantization', 'step6_model_deployment', 'completed']
        }

        step_order = step_orders.get(self.training_type, step_orders['deep_learning'])
        total_steps = len(step_order) - 1  # 不包括draft

        if self.current_step == 'failed':
            return 0
        elif self.current_step == 'completed':
            return 100

        try:
            current_index = step_order.index(self.current_step)
            return int((current_index / total_steps) * 100)
        except ValueError:
            return 0

    def can_proceed_to_next_step(self):
        """检查是否可以进入下一步"""
        if self.current_step == 'draft':
            return True

        # 根据训练类型和当前步骤检查
        if self.training_type == 'deep_learning':
            if self.current_step == 'step1_data_loading':
                return bool(self.step1_config.get('dataset_id'))
            elif self.current_step == 'step2_training_config':
                return bool(self.step2_config.get('epochs'))
            elif self.current_step == 'step3_model_training':
                return bool(self.training_task_id)
            elif self.current_step == 'step4_result_evaluation':
                return bool(self.step4_config.get('evaluation_completed'))
            elif self.current_step == 'step5_model_export':
                return bool(self.step5_config.get('export_completed'))

        elif self.training_type == 'reinforcement_learning':
            if self.current_step == 'step1_env_setup':
                return bool(self.step1_config.get('environment_id'))
            elif self.current_step == 'step2_training_config':
                return bool(self.step2_config.get('algorithm'))
            elif self.current_step == 'step3_model_training':
                return bool(self.training_task_id)
            elif self.current_step == 'step4_result_evaluation':
                return bool(self.step4_config.get('evaluation_completed'))
            elif self.current_step == 'step5_model_export':
                return bool(self.step5_config.get('export_completed'))

        elif self.training_type == 'large_model':
            if self.current_step == 'step1_data_loading':
                return bool(self.step1_config.get('dataset_id'))
            elif self.current_step == 'step2_training_config':
                return bool(self.step2_config.get('learning_rate'))
            elif self.current_step == 'step3_fine_tuning':
                return bool(self.training_task_id)
            elif self.current_step == 'step4_result_evaluation':
                return bool(self.step4_config.get('evaluation_completed'))
            elif self.current_step == 'step5_model_quantization':
                return bool(self.step5_config.get('quantization_completed'))
            elif self.current_step == 'step6_model_deployment':
                return bool(self.step6_config.get('deployment_completed'))

        return False

    def get_next_step(self):
        """获取下一步骤"""
        step_orders = {
            'deep_learning': ['draft', 'step1_data_loading', 'step2_training_config', 'step3_model_training', 'step4_result_evaluation', 'step5_model_export', 'completed'],
            'reinforcement_learning': ['draft', 'step1_env_setup', 'step2_training_config', 'step3_model_training', 'step4_result_evaluation', 'step5_model_export', 'completed'],
            'large_model': ['draft', 'step1_data_loading', 'step2_training_config', 'step3_fine_tuning', 'step4_result_evaluation', 'step5_model_quantization', 'step6_model_deployment', 'completed']
        }

        step_order = step_orders.get(self.training_type, step_orders['deep_learning'])
        try:
            current_index = step_order.index(self.current_step)
            if current_index < len(step_order) - 1:
                return step_order[current_index + 1]
        except ValueError:
            pass
        return None

    def get_step_config(self, step_name):
        """获取指定步骤的配置"""
        step_mapping = {
            'step1': self.step1_config,
            'step2': self.step2_config,
            'step3': self.step3_config,
            'step4': self.step4_config,
            'step5': self.step5_config,
            'step6': self.step6_config,
        }
        return step_mapping.get(step_name, {})

    def update_step_config(self, step_name, config_data):
        """更新指定步骤的配置"""
        if step_name == 'step1':
            self.step1_config.update(config_data)
        elif step_name == 'step2':
            self.step2_config.update(config_data)
        elif step_name == 'step3':
            self.step3_config.update(config_data)
        elif step_name == 'step4':
            self.step4_config.update(config_data)
        elif step_name == 'step5':
            self.step5_config.update(config_data)
        elif step_name == 'step6':
            self.step6_config.update(config_data)
        self.save()


class TrainingMetrics(models.Model):
    """训练指标记录模型"""
    
    workflow = models.ForeignKey(TrainingWorkflow, on_delete=models.CASCADE, related_name='metrics_records')
    
    # 训练基本信息
    epoch = models.IntegerField('训练轮次')
    step = models.IntegerField('训练步数', default=0)
    
    # 损失指标
    train_loss = models.FloatField('训练损失', null=True, blank=True)
    val_loss = models.FloatField('验证损失', null=True, blank=True)
    
    # 准确率指标
    train_accuracy = models.FloatField('训练准确率', null=True, blank=True)
    val_accuracy = models.FloatField('验证准确率', null=True, blank=True)
    
    # 目标检测特有指标
    precision = models.FloatField('精确率', null=True, blank=True)
    recall = models.FloatField('召回率', null=True, blank=True)
    map50 = models.FloatField('mAP@0.5', null=True, blank=True)
    map50_95 = models.FloatField('mAP@0.5:0.95', null=True, blank=True)
    
    # 训练环境指标
    learning_rate = models.FloatField('学习率', null=True, blank=True)
    gpu_memory = models.FloatField('GPU内存使用', null=True, blank=True)
    training_time = models.FloatField('训练时间(秒)', null=True, blank=True)
    
    # 其他指标
    additional_metrics = models.JSONField('其他指标', default=dict, blank=True)
    
    # 记录时间
    recorded_at = models.DateTimeField('记录时间', auto_now_add=True)
    
    class Meta:
        verbose_name = '训练指标'
        verbose_name_plural = '训练指标'
        db_table = 'training_metrics'
        ordering = ['-recorded_at']
        indexes = [
            models.Index(fields=['workflow', 'epoch']),
            models.Index(fields=['recorded_at']),
        ]
    
    def __str__(self):
        return f"{self.workflow.name} - Epoch {self.epoch}"


class ModelEvaluation(models.Model):
    """模型评估结果模型"""
    
    workflow = models.ForeignKey(TrainingWorkflow, on_delete=models.CASCADE, related_name='evaluations')
    
    # 评估基本信息
    evaluation_name = models.CharField('评估名称', max_length=200)
    model_path = models.CharField('模型路径', max_length=500)
    test_dataset_path = models.CharField('测试数据集路径', max_length=500)
    
    # 评估结果
    overall_accuracy = models.FloatField('总体准确率', null=True, blank=True)
    precision = models.FloatField('精确率', null=True, blank=True)
    recall = models.FloatField('召回率', null=True, blank=True)
    f1_score = models.FloatField('F1分数', null=True, blank=True)
    
    # 目标检测特有指标
    map50 = models.FloatField('mAP@0.5', null=True, blank=True)
    map50_95 = models.FloatField('mAP@0.5:0.95', null=True, blank=True)
    
    # 性能指标
    inference_time_ms = models.FloatField('推理时间(毫秒)', null=True, blank=True)
    fps = models.FloatField('FPS', null=True, blank=True)
    model_size_mb = models.FloatField('模型大小(MB)', null=True, blank=True)
    
    # 详细结果
    class_metrics = models.JSONField('各类别指标', default=dict, blank=True)
    confusion_matrix = models.JSONField('混淆矩阵', default=dict, blank=True)
    evaluation_report = models.JSONField('评估报告', default=dict, blank=True)
    
    # 评估状态
    status = models.CharField('评估状态', max_length=20, choices=[
        ('pending', '等待中'),
        ('running', '评估中'),
        ('completed', '已完成'),
        ('failed', '失败'),
    ], default='pending')
    
    # 时间信息
    started_at = models.DateTimeField('开始时间', null=True, blank=True)
    completed_at = models.DateTimeField('完成时间', null=True, blank=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    
    class Meta:
        verbose_name = '模型评估'
        verbose_name_plural = '模型评估'
        db_table = 'model_evaluation'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.workflow.name} - {self.evaluation_name}"


class InferenceModel(models.Model):
    """推理模型模型"""
    
    workflow = models.ForeignKey(TrainingWorkflow, on_delete=models.CASCADE, related_name='inference_models')
    
    # 模型基本信息
    model_name = models.CharField('模型名称', max_length=200)
    model_version = models.CharField('模型版本', max_length=50, default='1.0')
    model_path = models.CharField('模型路径', max_length=500)
    
    # 部署配置
    deployment_config = models.JSONField('部署配置', default=dict, blank=True)
    
    # 模型状态
    status = models.CharField('状态', max_length=20, choices=[
        ('preparing', '准备中'),
        ('ready', '就绪'),
        ('deployed', '已部署'),
        ('failed', '失败'),
        ('archived', '已归档'),
    ], default='preparing')
    
    # 性能信息
    inference_time_ms = models.FloatField('推理时间(毫秒)', null=True, blank=True)
    throughput_qps = models.FloatField('吞吐量(QPS)', null=True, blank=True)
    model_size_mb = models.FloatField('模型大小(MB)', null=True, blank=True)
    
    # 使用统计
    total_requests = models.IntegerField('总请求数', default=0)
    successful_requests = models.IntegerField('成功请求数', default=0)
    
    # 时间信息
    deployed_at = models.DateTimeField('部署时间', null=True, blank=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '推理模型'
        verbose_name_plural = '推理模型'
        db_table = 'inference_model'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.model_name} v{self.model_version}"
